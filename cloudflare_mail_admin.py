#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cloudflare临时邮箱管理员API - 完整功能
包含配置、API客户端和使用示例
"""

import requests
import json
import logging
from typing import Optional, Dict, List, Any
from dataclasses import dataclass
from urllib.parse import urljoin
from datetime import datetime

# ==================== 配置信息 ====================
WORKER_DOMAIN = "api.91gmail.cn"  # 后台域名 
EMAIL_DOMAIN = "91gmail.cn"  # 你的域名地址
ADMIN_PASSWORD = "yu6709"  # 你的管理员密码

# API配置
API_CONFIG = {
    "base_url": f"https://{WORKER_DOMAIN}",
    "admin_password": ADMIN_PASSWORD,
    "timeout": 30,
    "verify_ssl": True,
}

# ==================== 数据类 ====================
@dataclass
class MailItem:
    """邮件项数据类"""
    id: int
    message_id: str
    source: str
    address: str
    raw: str
    created_at: str


@dataclass
class MailListResponse:
    """邮件列表响应数据类"""
    results: List[MailItem]
    count: int
    limit: int
    offset: int


# ==================== API客户端类 ====================
class CloudflareMailAdminAPI:
    """Cloudflare临时邮箱管理员API客户端"""
    
    def __init__(
        self, 
        base_url: str,
        admin_password: Optional[str] = None,
        user_access_token: Optional[str] = None,
        timeout: int = 30,
        verify_ssl: bool = True
    ):
        """初始化API客户端"""
        self.base_url = base_url.rstrip('/')
        self.admin_password = admin_password
        self.user_access_token = user_access_token
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        self.session = requests.Session()
        
        # 设置认证头
        if admin_password:
            self.session.headers.update({'x-admin-auth': admin_password})
        elif user_access_token:
            self.session.headers.update({'x-user-access-token': user_access_token})
        else:
            raise ValueError("必须提供 admin_password 或 user_access_token 之一")
        
        # 设置默认头部
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'CloudflareMailAdmin-Python/1.0'
        })
    
    def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict] = None,
        data: Optional[Dict] = None
    ) -> requests.Response:
        """发送HTTP请求"""
        url = urljoin(self.base_url, endpoint)
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                timeout=self.timeout,
                verify=self.verify_ssl
            )
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            raise requests.RequestException(f"API请求失败: {e}")
    
    def get_mails(
        self,
        address: Optional[str] = None,
        keyword: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> MailListResponse:
        """获取邮件列表"""
        params = {
            'limit': limit,
            'offset': offset
        }
        
        if address:
            params['address'] = address
        if keyword:
            params['keyword'] = keyword
        
        response = self._make_request('GET', '/admin/mails', params=params)
        data = response.json()
        
        # 解析响应数据
        mail_items = []
        for item in data.get('results', []):
            mail_items.append(MailItem(
                id=item['id'],
                message_id=item.get('message_id', ''),
                source=item.get('source', ''),
                address=item['address'],
                raw=item['raw'],
                created_at=item['created_at']
            ))
        
        return MailListResponse(
            results=mail_items,
            count=data.get('count', 0),
            limit=limit,
            offset=offset
        )
    
    def get_unknown_mails(self, limit: int = 20, offset: int = 0) -> MailListResponse:
        """获取未知邮件列表（地址不在系统中的邮件）"""
        params = {'limit': limit, 'offset': offset}
        response = self._make_request('GET', '/admin/mails_unknow', params=params)
        data = response.json()
        
        mail_items = []
        for item in data.get('results', []):
            mail_items.append(MailItem(
                id=item['id'],
                message_id=item.get('message_id', ''),
                source=item.get('source', ''),
                address=item['address'],
                raw=item['raw'],
                created_at=item['created_at']
            ))
        
        return MailListResponse(
            results=mail_items,
            count=data.get('count', 0),
            limit=limit,
            offset=offset
        )
    
    def delete_mail(self, mail_id: int) -> bool:
        """删除指定邮件"""
        response = self._make_request('DELETE', f'/admin/mails/{mail_id}')
        data = response.json()
        return data.get('success', False)
    
    def search_mails_by_address(self, address: str, limit: int = 20, offset: int = 0) -> MailListResponse:
        """根据邮箱地址搜索邮件"""
        return self.get_mails(address=address, limit=limit, offset=offset)
    
    def search_mails_by_keyword(self, keyword: str, limit: int = 20, offset: int = 0) -> MailListResponse:
        """根据关键词搜索邮件"""
        return self.get_mails(keyword=keyword, limit=limit, offset=offset)


# ==================== 工具函数 ====================
def print_mail_info(mail_item: MailItem):
    """打印邮件信息"""
    print(f"  ID: {mail_item.id}")
    print(f"  地址: {mail_item.address}")
    print(f"  发件人: {mail_item.source}")
    print(f"  消息ID: {mail_item.message_id}")
    print(f"  创建时间: {mail_item.created_at}")
    # 只显示邮件内容的前100个字符
    raw_preview = mail_item.raw[:100] + "..." if len(mail_item.raw) > 100 else mail_item.raw
    print(f"  内容预览: {raw_preview}")
    print("-" * 50)


def test_connection():
    """测试API连接"""
    try:
        print("正在测试API连接...")
        print(f"API地址: {API_CONFIG['base_url']}")
        
        # 初始化API客户端
        api = CloudflareMailAdminAPI(**API_CONFIG)
        
        # 尝试获取邮件列表（只获取1条）
        mails = api.get_mails(limit=1)
        
        print("✅ API连接成功！")
        print(f"总邮件数: {mails.count}")
        print(f"当前页邮件数: {len(mails.results)}")
        
        if mails.results:
            mail = mails.results[0]
            print(f"示例邮件 - ID: {mail.id}, 地址: {mail.address}")
        
        return True
        
    except Exception as e:
        print("❌ API连接失败！")
        print(f"错误信息: {e}")
        print("\n请检查:")
        print("1. 网络连接是否正常")
        print("2. API地址是否正确")
        print("3. 管理员密码是否正确")
        print("4. 服务是否正在运行")
        return False


# ==================== 主要功能演示 ====================
def main_demo():
    """主要功能演示"""
    try:
        print("=" * 60)
        print("Cloudflare临时邮箱管理员API - 功能演示")
        print(f"API地址: {API_CONFIG['base_url']}")
        print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 初始化API客户端
        api = CloudflareMailAdminAPI(**API_CONFIG)
        
        # 1. 获取邮件列表
        print("\n1. 获取邮件列表")
        print("-" * 30)
        mails = api.get_mails(limit=5)
        print(f"总邮件数: {mails.count}")
        print(f"当前页邮件数: {len(mails.results)}")
        
        for mail in mails.results:
            print_mail_info(mail)
        
        # 2. 搜索功能演示
        print("\n2. 搜索功能演示")
        print("-" * 30)
        
        # 搜索特定地址
        search_address = input("请输入要搜索的邮箱地址（直接回车跳过）: ").strip()
        if search_address:
            address_mails = api.search_mails_by_address(search_address, limit=3)
            print(f"地址 '{search_address}' 的邮件数: {address_mails.count}")
            for mail in address_mails.results:
                print_mail_info(mail)
        
        # 搜索关键词
        search_keyword = input("请输入搜索关键词（直接回车跳过）: ").strip()
        if search_keyword:
            keyword_mails = api.search_mails_by_keyword(search_keyword, limit=3)
            print(f"包含关键词 '{search_keyword}' 的邮件数: {keyword_mails.count}")
            for mail in keyword_mails.results:
                print_mail_info(mail)
        
        # 3. 获取未知邮件
        print("\n3. 获取未知邮件")
        print("-" * 30)
        unknown_mails = api.get_unknown_mails(limit=3)
        print(f"未知邮件数: {unknown_mails.count}")
        
        for mail in unknown_mails.results:
            print_mail_info(mail)
        
        print("\n演示完成！")
        
    except Exception as e:
        print(f"演示过程中出错: {e}")


if __name__ == "__main__":
    print("选择操作:")
    print("1. 测试API连接")
    print("2. 运行功能演示")
    print("3. 自定义操作")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        test_connection()
    elif choice == "2":
        main_demo()
    elif choice == "3":
        # 自定义操作示例
        try:
            api = CloudflareMailAdminAPI(**API_CONFIG)
            print("API客户端已初始化，您可以调用以下方法:")
            print("- api.get_mails()")
            print("- api.search_mails_by_address('<EMAIL>')")
            print("- api.search_mails_by_keyword('关键词')")
            print("- api.get_unknown_mails()")
            print("- api.delete_mail(邮件ID)")
        except Exception as e:
            print(f"初始化失败: {e}")
    else:
        print("无效选择，运行连接测试")
        test_connection()
