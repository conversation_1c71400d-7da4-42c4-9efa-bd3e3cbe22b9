import requests
import json
import re
import base64
import quopri  # 添加quopri模块用于解码QUOTED-PRINTABLE

# 配置信息
WORKER_DOMAIN = "api.91gmail.cn"  # 后台域名
EMAIL_DOMAIN = "91gmail.cn"  # 域名地址
ADMIN_PASSWORD = "yu6709"  # 管理员密码


def extract_verification_code(raw_content):
    """从邮件内容中提取验证码"""
    print("正在分析邮件内容...")

    # 方法1: 尝试解析QUOTED-PRINTABLE编码的纯文本
    quoted_printable_match = re.search(r'Content-Transfer-Encoding: QUOTED-PRINTABLE\r\n.*?\r\n\r\n([\s\S]+?)(?:\r\n-+)', raw_content, re.IGNORECASE)
    if quoted_printable_match:
        print("发现QUOTED-PRINTABLE编码内容，正在解码...")
        quoted_content = quoted_printable_match.group(1)
        try:
            decoded_content = quopri.decodestring(quoted_content).decode('utf-8', errors='ignore')

            # 清理内容，移除编码符号
            clean_content = decoded_content.replace('=\r\n', '').replace('\r\n', '\n')
            lines = [line.strip() for line in clean_content.split('\n') if line.strip()]

            print(f"\n📧 邮件正文内容:")
            for line in lines[:15]:  # 显示前15行内容
                if line and len(line) < 150 and not line.startswith('http'):
                    print(f"   {line}")

            # 查找验证码
            verification_matches = re.findall(r'(\d{6})', decoded_content)
            if verification_matches:
                verification_code = verification_matches[0]
                print(f"\n✅ 从QUOTED-PRINTABLE内容中提取到验证码: {verification_code}")
                return verification_code
        except Exception as e:
            print(f"❌ QUOTED-PRINTABLE解码错误: {e}")

    # 方法2: 尝试解析Base64编码内容
    base64_match = re.search(r'Content-Transfer-Encoding: base64\r\n\r\n([A-Za-z0-9+/=\r\n]+?)(?:\r\n-+)', raw_content, re.IGNORECASE)
    if base64_match:
        print("发现Base64编码内容，正在解码...")
        base64_content = base64_match.group(1).replace('\r\n', '').replace('\n', '')
        try:
            decoded_content = base64.b64decode(base64_content).decode('utf-8', errors='ignore')

            # 清理和格式化内容
            lines = [line.strip() for line in decoded_content.split('\n') if line.strip()]

            print(f"\n📧 邮件正文内容:")
            for line in lines[:15]:
                if line and len(line) < 150 and not line.startswith('<'):
                    print(f"   {line}")

            verification_matches = re.findall(r'(\d{6})', decoded_content)
            if verification_matches:
                verification_code = verification_matches[0]
                print(f"\n✅ 从Base64内容中提取到验证码: {verification_code}")
                return verification_code
        except Exception as e:
            print(f"❌ Base64解码错误: {e}")

    # 方法3: 查找纯文本部分
    plain_text_match = re.search(r'Content-Type: text/plain.*?\r\n.*?\r\n\r\n([^-]+?)(?:\r\n-+)', raw_content, re.IGNORECASE)
    if plain_text_match:
        print("发现纯文本内容...")
        plain_text = plain_text_match.group(1)
        lines = [line.strip() for line in plain_text.split('\n') if line.strip()]

        print(f"\n📧 纯文本内容:")
        for line in lines[:10]:
            if line and len(line) < 150:
                print(f"   {line}")

        verification_matches = re.findall(r'(\d{6})', plain_text)
        if verification_matches:
            verification_code = verification_matches[0]
            print(f"\n✅ 从纯文本中提取到验证码: {verification_code}")
            return verification_code

    # 方法4: 直接在原始内容中查找验证码（备用方案）
    print("尝试从原始内容中查找验证码...")
    verification_matches = re.findall(r'(\d{6})', raw_content)
    if verification_matches:
        verification_code = verification_matches[0]
        print(f"\n✅ 从原始内容中提取到验证码: {verification_code}")
        return verification_code

    print("❌ 未找到验证码")
    return None


def get_verification_code_by_hidden_email(hidden_email_address):
    """通过隐藏邮箱地址查找验证码"""
    try:
        print(f"\n正在搜索隐藏邮箱 '{hidden_email_address}' 的验证码...")

        # 使用管理员权限搜索所有邮件
        res = requests.get(
            f"https://{WORKER_DOMAIN}/admin/mails",
            params={
                "limit": 50,  # 获取更多邮件以提高搜索命中率
                "offset": 0
            },
            headers={
                'x-admin-auth': ADMIN_PASSWORD,
                "Content-Type": "application/json"
            }
        )

        if res.status_code == 200:
            data = res.json()

            if data.get('results'):
                print(f"正在检查 {len(data['results'])} 封邮件...")

                for mail in data['results']:
                    raw_content = mail.get('raw', '')

                    # 查找隐藏邮箱地址
                    hide_email_match = re.search(r'Hide My Email[^<]*<([^>]+@icloud\.com(?:\.cn)?)>', raw_content)
                    if hide_email_match:
                        found_hidden_email = hide_email_match.group(1)

                        # 如果找到匹配的隐藏邮箱地址
                        if found_hidden_email.lower() == hidden_email_address.lower():
                            print(f"\n✅ 找到匹配的隐藏邮箱邮件:")
                            print(f"  发件人: {mail.get('source', '未知')}")
                            print(f"  时间: {mail.get('created_at', '未知')}")
                            print(f"  隐藏邮箱: {found_hidden_email}")

                            # 提取收件人信息
                            to_match = re.search(r'To: (.*?)(?:\r\n|\n)(?![ \t])', raw_content, re.IGNORECASE)
                            if to_match:
                                print(f"  收件人: {to_match.group(1)}")

                            # 提取验证码
                            print(f"\n正在从邮件中提取验证码...")
                            verification_code = extract_verification_code(raw_content)
                            if verification_code:
                                print(f"\n🎉 成功找到验证码: {verification_code}")
                                return verification_code
                            else:
                                print("❌ 在该邮件中未找到验证码")
                                continue

            print(f"❌ 未找到隐藏邮箱 '{hidden_email_address}' 的邮件")
            return None
        else:
            print(f"搜索失败: {res.status_code}")
            return None

    except Exception as e:
        print(f"搜索出错: {e}")
        return None


def main():
    """主函数 - 通过隐藏邮箱地址获取验证码"""
    print("🔍 Cloudflare Mail 验证码获取工具")
    print("=" * 50)

    hidden_email = input("请输入隐藏邮箱地址 (例如: <EMAIL>): ").strip()

    if not hidden_email:
        print("❌ 隐藏邮箱地址不能为空")
        return

    # 验证输入格式
    if not re.match(r'^[^@]+@icloud\.com(?:\.cn)?$', hidden_email):
        print("❌ 请输入有效的 iCloud 隐藏邮箱地址 (格式: <EMAIL> 或 <EMAIL>)")
        return

    print(f"🔎 正在查找隐藏邮箱 '{hidden_email}' 的验证码...")
    verification_code = get_verification_code_by_hidden_email(hidden_email)

    if verification_code:
        print(f"\n🎉 成功获取验证码: {verification_code}")
        print("=" * 50)
    else:
        print(f"\n❌ 未能找到隐藏邮箱 '{hidden_email}' 的验证码")
        print("💡 提示: 请确认隐藏邮箱地址正确，且该邮箱最近有收到验证码邮件")
        print("=" * 50)


if __name__ == "__main__":
    main()